import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useGetBookByIdQuery, useUpdateBookMutation } from '../app/api/apiSlice';
import toast from 'react-hot-toast';

// Zod schema for edit form validation (similar to CreateBook, but ISBN might not be required if not changed)
const bookEditSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  author: z.string().min(1, 'Author is required'),
  genre: z.string().min(1, 'Genre is required'),
  // ISBN validation: 10 or 13 digits, allowing hyphens but not requiring them in final value
  isbn: z.string()
    .min(10, 'ISBN must be at least 10 characters')
    .max(13, 'ISBN must be at most 13 characters')
    .regex(/^[0-9-]+$/, 'ISBN can only contain numbers and hyphens')
    .transform(val => val.replace(/-/g, '')) // Remove hyphens for final submission/validation
    .refine(val => /^\d{10}(\d{3})?$/.test(val), 'Invalid ISBN format (must be 10 or 13 digits)'),
  description: z.string().optional(),
  copies: z.number().int().min(0, 'Copies cannot be negative').nonNullable(),
});

type BookEditFormData = z.infer<typeof bookEditSchema>;

const EditBook: React.FC = () => {
  const { id } = useParams<{ id: string }>(); // Get book ID from URL
  const navigate = useNavigate();

  // Fetch existing book data
  const { data: book, isLoading: isBookLoading, isError: isBookError, error: bookError } = useGetBookByIdQuery(id!);
  // Mutation hook for updating
  const [updateBook, { isLoading: isUpdating }] = useUpdateBookMutation();

  const {
    register,
    handleSubmit,
    reset, // Use reset to populate form fields
    formState: { errors },
  } = useForm<BookEditFormData>({
    resolver: zodResolver(bookEditSchema),
  });

  // Populate form fields once book data is loaded
  useEffect(() => {
    if (book) {
      reset({
        title: book.title,
        author: book.author,
        genre: book.genre,
        isbn: book.isbn,
        description: book.description,
        copies: book.copies,
      });
    }
  }, [book, reset]);

  const onSubmit = async (data: BookEditFormData) => {
    if (!id) {
      toast.error('Book ID is missing for update.');
      return;
    }
    try {
      // Send the entire form data along with the ID for the update mutation
      await updateBook({ _id: id, ...data }).unwrap();
      toast.success('Book updated successfully!');
      navigate('/books'); // Redirect to book list
    } catch (err: any) {
      toast.error(`Failed to update book: ${err?.data?.message || err.message || 'Unknown error'}`);
    }
  };

  if (isBookLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-lg text-gray-600">Loading book data for editing...</p>
      </div>
    );
  }

  if (isBookError) {
    return (
      <div className="text-center py-8 text-red-600">
        <p className="font-bold text-xl">Error loading book!</p>
        <p>{bookError?.data?.message || 'Could not fetch book details. Please try again later.'}</p>
        <button onClick={() => navigate('/books')} className="mt-4 bg-gray-200 text-gray-700 py-2 px-4 rounded hover:bg-gray-300">
          Go to Book List
        </button>
      </div>
    );
  }

  if (!book) {
    return (
      <div className="text-center py-8 text-gray-600">
        <p className="font-bold text-xl">Book not found.</p>
        <button onClick={() => navigate('/books')} className="mt-4 bg-gray-200 text-gray-700 py-2 px-4 rounded hover:bg-gray-300">
          Go to Book List
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white p-8 rounded-lg shadow-xl max-w-2xl mx-auto">
      <h2 className="text-4xl font-extrabold text-gray-900 mb-8 text-center">Edit "{book.title}"</h2>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <label htmlFor="title" className="block text-gray-700 text-sm font-bold mb-2">Title<span className="text-red-500">*</span></label>
          <input
            type="text"
            id="title"
            {...register('title')}
            className="shadow-sm appearance-none border border-gray-300 rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {errors.title && <p className="text-red-500 text-xs italic mt-1">{errors.title.message}</p>}
        </div>

        <div>
          <label htmlFor="author" className="block text-gray-700 text-sm font-bold mb-2">Author<span className="text-red-500">*</span></label>
          <input
            type="text"
            id="author"
            {...register('author')}
            className="shadow-sm appearance-none border border-gray-300 rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {errors.author && <p className="text-red-500 text-xs italic mt-1">{errors.author.message}</p>}
        </div>

        <div>
          <label htmlFor="genre" className="block text-gray-700 text-sm font-bold mb-2">Genre<span className="text-red-500">*</span></label>
          <input
            type="text"
            id="genre"
            {...register('genre')}
            className="shadow-sm appearance-none border border-gray-300 rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {errors.genre && <p className="text-red-500 text-xs italic mt-1">{errors.genre.message}</p>}
        </div>

        <div>
          <label htmlFor="isbn" className="block text-gray-700 text-sm font-bold mb-2">ISBN<span className="text-red-500">*</span></label>
          <input
            type="text"
            id="isbn"
            {...register('isbn')}
            className="shadow-sm appearance-none border border-gray-300 rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {errors.isbn && <p className="text-red-500 text-xs italic mt-1">{errors.isbn.message}</p>}
        </div>

        <div>
          <label htmlFor="description" className="block text-gray-700 text-sm font-bold mb-2">Description</label>
          <textarea
            id="description"
            {...register('description')}
            rows={4}
            className="shadow-sm appearance-none border border-gray-300 rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-y"
          ></textarea>
        </div>

        <div>
          <label htmlFor="copies" className="block text-gray-700 text-sm font-bold mb-2">Total Copies<span className="text-red-500">*</span></label>
          <input
            type="number"
            id="copies"
            {...register('copies', { valueAsNumber: true })}
            min="0"
            className="shadow-sm appearance-none border border-gray-300 rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {errors.copies && <p className="text-red-500 text-xs italic mt-1">{errors.copies.message}</p>}
        </div>

        <button
          type="submit"
          disabled={isUpdating}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg focus:outline-none focus:shadow-outline w-full text-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 disabled:opacity-60 disabled:cursor-not-allowed"
        >
          {isUpdating ? 'Updating Book...' : 'Update Book'}
        </button>
      </form>
    </div>
  );
};

export default EditBook;