"use client"

import { useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useGetBookByIdQuery, useUpdateBookMutation } from '@/app/api/apiSlice'
import toast from 'react-hot-toast'
import { ArrowLeft, BookOpen } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'

// Zod schema for edit form validation
const bookEditSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  author: z.string().min(1, 'Author is required'),
  genre: z.string().min(1, 'Genre is required'),
  isbn: z.string()
    .min(10, 'ISBN must be at least 10 characters')
    .max(17, 'ISBN must be at most 17 characters (including hyphens)')
    .regex(/^[0-9-]+$/, 'ISBN can only contain numbers and hyphens')
    .transform(val => val.replace(/-/g, ''))
    .refine(val => /^\d{10}(\d{3})?$/.test(val), 'Invalid ISBN format (must be 10 or 13 digits)'),
  description: z.string().optional(),
  copies: z.number().int().min(0, 'Copies cannot be negative'),
})

type BookEditFormData = z.infer<typeof bookEditSchema>

const genres = [
  'FICTION',
  'NON_FICTION',
  'SCIENCE',
  'HISTORY',
  'BIOGRAPHY',
  'FANTASY',
  'MYSTERY',
  'ROMANCE',
  'THRILLER',
  'HORROR',
  'POETRY',
  'DRAMA',
  'PHILOSOPHY',
  'RELIGION',
  'SELF_HELP',
  'BUSINESS',
  'TECHNOLOGY',
  'HEALTH',
  'TRAVEL',
  'COOKING',
  'ART',
  'MUSIC',
  'SPORTS',
  'CHILDREN',
  'YOUNG_ADULT'
]

export default function EditBook() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  const { data: book, isLoading: isBookLoading, isError: isBookError, error: bookError } = useGetBookByIdQuery(id!)
  const [updateBook, { isLoading: isUpdating }] = useUpdateBookMutation()

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<BookEditFormData>({
    resolver: zodResolver(bookEditSchema),
  })

  const selectedGenre = watch('genre')

  // Populate form fields once book data is loaded
  useEffect(() => {
    if (book) {
      reset({
        title: book.title,
        author: book.author,
        genre: book.genre,
        isbn: book.isbn,
        description: book.description || '',
        copies: book.copies,
      })
    }
  }, [book, reset])

  const onSubmit = async (data: BookEditFormData) => {
    if (!id) {
      toast.error('Book ID is missing for update.')
      return
    }

    try {
      const updateData = {
        _id: id,
        ...data,
        available: data.copies > 0, // Set available based on copies count
      }

      await updateBook(updateData).unwrap()
      toast.success('Book updated successfully!')
      navigate('/books')
    } catch (err: any) {
      console.error('Error updating book:', err)
      const errorMessage =
        err?.data?.message ||
        err?.message ||
        'Failed to update book'
      toast.error(errorMessage)
    }
  }

  // Handle loading state
  if (isBookLoading) {
    return (
      <div className="w-full max-w-2xl mx-auto p-6">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <Skeleton className="h-8 w-8" />
              <Skeleton className="h-8 w-48" />
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }

  // Handle error state
  if (isBookError || !book) {
    const errorMessage =
      bookError && typeof bookError === 'object' && 'data' in bookError && bookError.data && typeof bookError.data === 'object' && 'message' in bookError.data
        ? (bookError.data as { message: string }).message
        : 'Book not found'

    return (
      <div className="w-full max-w-2xl mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-6 w-6" />
              Edit Book
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="text-destructive mb-4">Error: {errorMessage}</div>
              <Button onClick={() => navigate('/books')} variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Books
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-6 w-6" />
            Edit "{book.title}"
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/books')}
            className="w-fit"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Books
          </Button>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Title Field */}
            <div className="space-y-2">
              <Label htmlFor="title">
                Title <span className="text-destructive">*</span>
              </Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="e.g., The Hitchhiker's Guide to the Galaxy"
              />
              {errors.title && (
                <p className="text-sm text-destructive">{errors.title.message}</p>
              )}
            </div>

            {/* Author Field */}
            <div className="space-y-2">
              <Label htmlFor="author">
                Author <span className="text-destructive">*</span>
              </Label>
              <Input
                id="author"
                {...register('author')}
                placeholder="e.g., Douglas Adams"
              />
              {errors.author && (
                <p className="text-sm text-destructive">{errors.author.message}</p>
              )}
            </div>

            {/* Genre Field */}
            <div className="space-y-2">
              <Label htmlFor="genre">
                Genre <span className="text-destructive">*</span>
              </Label>
              <Select
                value={selectedGenre}
                onValueChange={(value) => setValue('genre', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a genre" />
                </SelectTrigger>
                <SelectContent>
                  {genres.map((genre) => (
                    <SelectItem key={genre} value={genre}>
                      {genre.replace('_', ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.genre && (
                <p className="text-sm text-destructive">{errors.genre.message}</p>
              )}
            </div>

            {/* ISBN Field */}
            <div className="space-y-2">
              <Label htmlFor="isbn">
                ISBN <span className="text-destructive">*</span>
              </Label>
              <Input
                id="isbn"
                {...register('isbn')}
                placeholder="e.g., 978-0345391803 or 0345391802"
              />
              {errors.isbn && (
                <p className="text-sm text-destructive">{errors.isbn.message}</p>
              )}
            </div>

            {/* Description Field */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Brief description of the book (optional)"
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description.message}</p>
              )}
            </div>

            {/* Copies Field */}
            <div className="space-y-2">
              <Label htmlFor="copies">
                Total Copies <span className="text-destructive">*</span>
              </Label>
              <Input
                id="copies"
                type="number"
                min="0"
                {...register('copies', { valueAsNumber: true })}
              />
              {errors.copies && (
                <p className="text-sm text-destructive">{errors.copies.message}</p>
              )}
            </div>

            {/* Submit Button */}
            <Button type="submit" disabled={isUpdating} className="w-full">
              {isUpdating ? 'Updating Book...' : 'Update Book'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}